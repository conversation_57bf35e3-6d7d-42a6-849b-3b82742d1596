import AdData from './ad-data';
import CardList from './card-list';
import DataCard from './card-list/card';
import { IBusinessData } from './const';
import CustomSummary from './custom-summary';
import DiagnosisData from './diagnosis-data';
import ShopHeaderCard from './shop-header-card';
import { useDataState } from './store/useDataState';
import Rank from './rank';
import ShopMatch from './shop-match';
import ConsumerAnalysis from './consumer-analysis';
import UserVector from './user-vector';
import { useRequest } from 'ahooks';
import { queryData as getData } from '@/services';
import styled from 'styled-components';
import AnnualBenefits from './annual-benefits';

const Container = styled.div`
  padding: 0 0.8em;
`;

interface IProps {
  data: IBusinessData;
  hiddenFields: string[];
  summaryTemplate: string;
}
export default function RightTargetData(props: IProps) {
  const { data: businessData, hiddenFields, summaryTemplate } = props;
  const { isChild, isMultiShops, shopList, dateRange, pid, shopIdList } = useDataState();
  console.log(businessData, 'businessData');

  const { data: yuntuData } = useRequest(
    async () => {
      const [startDate, endDate] = dateRange || [];
      if (isMultiShops || !startDate || !endDate || !shopIdList.length) return {};
      const res = await getData(
        {
          shopIdList,
          pid,
          startDate: dateRange?.[0],
          endDate: dateRange?.[1],
        },
        'xibao_year_report_yuntu_application',
      );
      const ruleDataList = res.applicationDataList?.[0]?.ruleDataList || [];
      let graphData;
      let structureData;
      let competivence;
      ruleDataList.forEach((item) => {
        if (item.ruleCode === 'yuntu_poi_pic_rule') {
          graphData = item.values?.[0];
        } else if (item.ruleCode === 'yuntu_poi_portrait_structure_rule') {
          structureData = item.values?.[0];
        } else if (item.ruleCode === 'yuntu_poi_competitiveness_rule') {
          competivence = item.values?.[0]?.competitiveness;
        }
      });
      return {
        graphData,
        structureData,
        competivence,
      };
    },
    {
      refreshDeps: [shopList, pid, dateRange],
    },
  );

  return (
    <Container>
      {isChild && (
        <ShopHeaderCard
          competivence={yuntuData?.competivence}
          duration={businessData?.dataResult?.sign_duration}
          right_label={businessData?.dataResult?.right_label}
          situation={businessData?.dataResult?.business_situation}
          score_level={businessData?.dataResult?.score_level}
        />
      )}

      {!isMultiShops && isChild ? <AnnualBenefits data={businessData?.dataResult} /> : null}

      <CardList
        // @ts-ignore 自动化截图时兼容字段
        hiddenFields={businessData?.hiddenFields || hiddenFields}
        data={businessData}
      />
      {businessData && !businessData?.recharge ? (
        <DataCard data={{ label: '广告投放数据', children: [] }}>
          <AdData />
        </DataCard>
      ) : null}
      {!isMultiShops && isChild ? (
        <>
          {/* 内容互动， 榜单 */}
          <Rank data={businessData?.dataResult || {}} />
          {/* 同行竞争 */}
          <ShopMatch
            graphData={yuntuData?.graphData?.around2kmsametypeheatgraph}
            center={
              yuntuData?.graphData?.companyxy ? yuntuData?.graphData?.companyxy.split(',') : []
            }
          />
          {/* 客群分析 */}
          <ConsumerAnalysis data={yuntuData} />
          {/* 潜在商机 */}
          <UserVector
            graphData={yuntuData?.graphData?.around1kmgeodaugraph}
            age={yuntuData?.structureData?.top_age}
            gender={yuntuData?.structureData?.top_sex}
            has_car={yuntuData?.structureData?.top_car}
            center={
              yuntuData?.graphData?.companyxy ? yuntuData?.graphData?.companyxy.split(',') : []
            }
            education={yuntuData?.structureData?.top_edu}
            percent={yuntuData?.structureData?.potential_ratio}
          />
        </>
      ) : null}
      <CustomSummary customSummary={summaryTemplate} />
      {isMultiShops || !isChild ? null : <DiagnosisData goodsNewsInfo={businessData} />}
    </Container>
  );
}
